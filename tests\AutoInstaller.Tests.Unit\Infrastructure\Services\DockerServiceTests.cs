using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using Docker.DotNet;
using Docker.DotNet.Models;
using AutoInstaller.Infrastructure.Services;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Core.Interfaces;

namespace AutoInstaller.Tests.Unit.Infrastructure.Services;

/// <summary>
/// Unit tests for DockerService
/// </summary>
public class DockerServiceTests : IDisposable
{
    private readonly Mock<IDockerClient> _mockDockerClient;
    private readonly Mock<IContainerOperations> _mockContainerOperations;
    private readonly Mock<IImageOperations> _mockImageOperations;
    private readonly Mock<ILogger<DockerService>> _mockLogger;
    private readonly DockerService _dockerService;

    public DockerServiceTests()
    {
        _mockDockerClient = new Mock<IDockerClient>();
        _mockContainerOperations = new Mock<IContainerOperations>();
        _mockImageOperations = new Mock<IImageOperations>();
        _mockLogger = new Mock<ILogger<DockerService>>();

        _mockDockerClient.Setup(x => x.Containers).Returns(_mockContainerOperations.Object);
        _mockDockerClient.Setup(x => x.Images).Returns(_mockImageOperations.Object);

        _dockerService = new DockerService(_mockDockerClient.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CreateContainerAsync_WithValidParameters_ShouldReturnContainerId()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:latest");
        var containerName = "test-container";
        var expectedContainerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        var createResponse = new CreateContainerResponse
        {
            ID = expectedContainerId,
            Warnings = new List<string>()
        };

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createResponse);

        // Act
        var result = await _dockerService.CreateContainerAsync(imageTag, containerName);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(expectedContainerId);

        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(
                It.Is<CreateContainerParameters>(p => 
                    p.Image == imageTag.Value && 
                    p.Name == containerName),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CreateContainerAsync_WithEnvironmentVariables_ShouldIncludeEnvVars()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:latest");
        var containerName = "test-container";
        var envVars = new List<EnvironmentVariable>
        {
            EnvironmentVariable.Create("NODE_ENV", "production"),
            EnvironmentVariable.Create("PORT", "3000")
        };
        var expectedContainerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        var createResponse = new CreateContainerResponse
        {
            ID = expectedContainerId,
            Warnings = new List<string>()
        };

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createResponse);

        // Act
        var result = await _dockerService.CreateContainerAsync(imageTag, containerName, envVars);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(expectedContainerId);

        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(
                It.Is<CreateContainerParameters>(p => 
                    p.Env.Count == 2 &&
                    p.Env.Contains("NODE_ENV=production") &&
                    p.Env.Contains("PORT=3000")),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CreateContainerAsync_WithDockerApiException_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:latest");
        var containerName = "test-container";

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerApiException(System.Net.HttpStatusCode.BadRequest, "Invalid image"));

        // Act & Assert
        await _dockerService.Invoking(s => s.CreateContainerAsync(imageTag, containerName))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Failed to create container: *Invalid image*");
    }

    [Fact]
    public async Task CreateContainerAsync_WithWarnings_ShouldLogWarnings()
    {
        // Arrange
        var imageTag = ImageTag.From("nginx:latest");
        var containerName = "test-container";
        var expectedContainerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        var createResponse = new CreateContainerResponse
        {
            ID = expectedContainerId,
            Warnings = new List<string> { "Warning 1", "Warning 2" }
        };

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createResponse);

        // Act
        var result = await _dockerService.CreateContainerAsync(imageTag, containerName);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(expectedContainerId);

        // Verify warnings were logged (we can't easily verify the exact log calls with Moq,
        // but we can verify the operation completed successfully)
        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task StartContainerAsync_WithValidId_ShouldReturnTrue()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.StartContainerAsync(
                containerId.Value, 
                It.IsAny<ContainerStartParameters>(), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _dockerService.StartContainerAsync(containerId);

        // Assert
        result.Should().BeTrue();

        _mockContainerOperations.Verify(
            x => x.StartContainerAsync(
                containerId.Value, 
                It.IsAny<ContainerStartParameters>(), 
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task StartContainerAsync_WithInvalidId_ShouldReturnFalse()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.StartContainerAsync(
                containerId.Value, 
                It.IsAny<ContainerStartParameters>(), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _dockerService.StartContainerAsync(containerId);

        // Assert
        result.Should().BeFalse();

        _mockContainerOperations.Verify(
            x => x.StartContainerAsync(
                containerId.Value, 
                It.IsAny<ContainerStartParameters>(), 
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task StartContainerAsync_WithDockerException_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var containerId = ContainerId.From("a1b2c3d4e5f6789012345678901234567890123456789012345678901234");

        _mockContainerOperations
            .Setup(x => x.StartContainerAsync(
                containerId.Value, 
                It.IsAny<ContainerStartParameters>(), 
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DockerApiException(System.Net.HttpStatusCode.NotFound, "Container not found"));

        // Act & Assert
        await _dockerService.Invoking(s => s.StartContainerAsync(containerId))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Failed to start container: *Container not found*");
    }

    [Fact]
    public void Constructor_WithNullDockerClient_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var action = () => new DockerService(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("dockerClient");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var action = () => new DockerService(_mockDockerClient.Object, null!);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("logger");
    }

    [Fact]
    public void Constructor_WithLoggerOnly_ShouldCreateDockerClient()
    {
        // Act
        using var dockerService = new DockerService(_mockLogger.Object);

        // Assert
        dockerService.Should().NotBeNull();
        // The service should be created successfully with an internal Docker client
    }

    [Fact]
    public void Dispose_ShouldDisposeResources()
    {
        // Arrange
        var dockerService = new DockerService(_mockDockerClient.Object, _mockLogger.Object);

        // Act
        dockerService.Dispose();

        // Assert
        // Verify that Dispose can be called without throwing
        // Multiple calls to Dispose should be safe
        dockerService.Dispose();
    }

    [Theory]
    [InlineData("nginx:latest")]
    [InlineData("redis:alpine")]
    [InlineData("postgres:13")]
    public async Task CreateContainerAsync_WithDifferentImages_ShouldUseCorrectImage(string imageTagValue)
    {
        // Arrange
        var imageTag = ImageTag.From(imageTagValue);
        var containerName = "test-container";
        var expectedContainerId = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234";

        var createResponse = new CreateContainerResponse
        {
            ID = expectedContainerId,
            Warnings = new List<string>()
        };

        _mockContainerOperations
            .Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(createResponse);

        // Act
        var result = await _dockerService.CreateContainerAsync(imageTag, containerName);

        // Assert
        result.Should().NotBeNull();
        result.Value.Should().Be(expectedContainerId);

        _mockContainerOperations.Verify(
            x => x.CreateContainerAsync(
                It.Is<CreateContainerParameters>(p => p.Image == imageTagValue),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    public void Dispose()
    {
        _dockerService?.Dispose();
    }
}
